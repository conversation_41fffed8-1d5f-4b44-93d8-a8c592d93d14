/**
 * @file apps/web-admin/src/server/utils/validation.ts
 * @description Data validation utilities for admin operations
 */

import 'server-only';
import { z } from 'zod';

// ========================================
// VALIDATION RESULT TYPE
// ========================================

export type ValidationResult<T = unknown> = {
  success: boolean;
  data?: T;
  errors?: Array<{
    field: string;
    message: string;
    code: string;
  }>;
};

// ========================================
// VALIDATION SCHEMAS
// ========================================

const UserDataValidationSchema = z.object({
  email: z.string().email('Invalid email address'),
  firstName: z.string().min(1, 'First name is required').max(50, 'First name too long'),
  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),
  role: z.enum(['user', 'premium', 'enterprise']),
  status: z.enum(['active', 'inactive', 'banned', 'pending']).default('active'),
  permissions: z.array(z.string()).optional(),
  metadata: z.record(z.unknown()).optional(),
});

const AdminDataValidationSchema = z.object({
  email: z.string().email('Invalid email address'),
  firstName: z.string().min(1, 'First name is required').max(50, 'First name too long'),
  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),
  roles: z.array(z.string()).min(1, 'At least one role is required'),
  permissions: z.array(z.string()).default([]),
  status: z.enum(['active', 'inactive', 'suspended']).default('active'),
});

const SystemSettingsValidationSchema = z.object({
  siteName: z.string().min(1, 'Site name is required').max(100, 'Site name too long'),
  siteDescription: z.string().max(500, 'Description too long').optional(),
  maintenanceMode: z.boolean().default(false),
  registrationEnabled: z.boolean().default(true),
  emailVerificationRequired: z.boolean().default(true),
  maxUsersPerAccount: z.number().min(1).max(10000).default(100),
  sessionTimeout: z.number().min(5).max(1440).default(60), // minutes
  passwordPolicy: z.object({
    minLength: z.number().min(6).max(128).default(8),
    requireUppercase: z.boolean().default(true),
    requireLowercase: z.boolean().default(true),
    requireNumbers: z.boolean().default(true),
    requireSpecialChars: z.boolean().default(false),
    maxAge: z.number().min(30).max(365).default(90), // days
  }).optional(),
  rateLimiting: z.object({
    enabled: z.boolean().default(true),
    requestsPerMinute: z.number().min(1).max(1000).default(100),
    requestsPerHour: z.number().min(1).max(10000).default(1000),
  }).optional(),
});

// ========================================
// VALIDATION FUNCTIONS
// ========================================

/**
 * Validate user data
 */
export function validateUserData(data: unknown): ValidationResult {
  try {
    const validatedData = UserDataValidationSchema.parse(data);
    return {
      success: true,
      data: validatedData,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code,
        })),
      };
    }
    return {
      success: false,
      errors: [{
        field: 'unknown',
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
      }],
    };
  }
}

/**
 * Validate admin data
 */
export function validateAdminData(data: unknown): ValidationResult {
  try {
    const validatedData = AdminDataValidationSchema.parse(data);
    return {
      success: true,
      data: validatedData,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code,
        })),
      };
    }
    return {
      success: false,
      errors: [{
        field: 'unknown',
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
      }],
    };
  }
}

/**
 * Validate system settings
 */
export function validateSystemSettings(data: unknown): ValidationResult {
  try {
    const validatedData = SystemSettingsValidationSchema.parse(data);
    return {
      success: true,
      data: validatedData,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code,
        })),
      };
    }
    return {
      success: false,
      errors: [{
        field: 'unknown',
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
      }],
    };
  }
}

/**
 * Validate email address
 */
export function validateEmail(email: string): ValidationResult<string> {
  try {
    const validatedEmail = z.string().email().parse(email);
    return {
      success: true,
      data: validatedEmail,
    };
  } catch {
    return {
      success: false,
      errors: [{
        field: 'email',
        message: 'Invalid email address',
        code: 'INVALID_EMAIL',
      }],
    };
  }
}

/**
 * Validate password strength
 */
export function validatePassword(password: string, policy?: {
  minLength?: number;
  requireUppercase?: boolean;
  requireLowercase?: boolean;
  requireNumbers?: boolean;
  requireSpecialChars?: boolean;
}): ValidationResult<string> {
  const errors: Array<{ field: string; message: string; code: string }> = [];
  
  const minLength = policy?.minLength || 8;
  const requireUppercase = policy?.requireUppercase ?? true;
  const requireLowercase = policy?.requireLowercase ?? true;
  const requireNumbers = policy?.requireNumbers ?? true;
  const requireSpecialChars = policy?.requireSpecialChars ?? false;

  if (password.length < minLength) {
    errors.push({
      field: 'password',
      message: `Password must be at least ${minLength} characters long`,
      code: 'PASSWORD_TOO_SHORT',
    });
  }

  if (requireUppercase && !/[A-Z]/.test(password)) {
    errors.push({
      field: 'password',
      message: 'Password must contain at least one uppercase letter',
      code: 'PASSWORD_MISSING_UPPERCASE',
    });
  }

  if (requireLowercase && !/[a-z]/.test(password)) {
    errors.push({
      field: 'password',
      message: 'Password must contain at least one lowercase letter',
      code: 'PASSWORD_MISSING_LOWERCASE',
    });
  }

  if (requireNumbers && !/\d/.test(password)) {
    errors.push({
      field: 'password',
      message: 'Password must contain at least one number',
      code: 'PASSWORD_MISSING_NUMBER',
    });
  }

  if (requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push({
      field: 'password',
      message: 'Password must contain at least one special character',
      code: 'PASSWORD_MISSING_SPECIAL_CHAR',
    });
  }

  if (errors.length > 0) {
    return {
      success: false,
      errors,
    };
  }

  return {
    success: true,
    data: password,
  };
}

/**
 * Validate UUID format
 */
export function validateUUID(uuid: string): ValidationResult<string> {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  
  if (!uuidRegex.test(uuid)) {
    return {
      success: false,
      errors: [{
        field: 'uuid',
        message: 'Invalid UUID format',
        code: 'INVALID_UUID',
      }],
    };
  }

  return {
    success: true,
    data: uuid,
  };
}

/**
 * Sanitize string input to prevent XSS
 */
export function sanitizeString(input: string): string {
  if (typeof input !== 'string') {
    return '';
  }
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove basic HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .substring(0, 10000); // Limit length
}

/**
 * Validate and sanitize object keys
 */
export function validateObjectKeys(obj: Record<string, unknown>, allowedKeys: string[]): ValidationResult<Record<string, unknown>> {
  const sanitizedObj: Record<string, unknown> = {};
  const errors: Array<{ field: string; message: string; code: string }> = [];

  for (const [key, value] of Object.entries(obj)) {
    if (!allowedKeys.includes(key)) {
      errors.push({
        field: key,
        message: `Field '${key}' is not allowed`,
        code: 'FIELD_NOT_ALLOWED',
      });
      continue;
    }

    sanitizedObj[key] = value;
  }

  if (errors.length > 0) {
    return {
      success: false,
      errors,
    };
  }

  return {
    success: true,
    data: sanitizedObj,
  };
}
