{"version": 3, "file": "fcm-client.js", "sourceRoot": "", "sources": ["../../src/client/fcm-client.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BH,sCAgBC;AAKD,kCAUC;AAKD,4CAeC;AAKD,oDAeC;AA9ED,2CAA2C;AAC3C,uBAAuB;AACvB,2CAA2C;AAE3C;;GAEG;AACH,SAAsB,aAAa;;;YACjC,IAAI,CAAC;gBACH,gFAAgF;gBAChF,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;gBAExC,sBAAO;wBACL,OAAO,EAAE,IAAI;wBACb,KAAK,EAAE,qBAAc,IAAI,CAAC,GAAG,EAAE,CAAE;qBAClC,EAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;gBACxD,sBAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;qBAChE,EAAC;YACJ,CAAC;;;;CACF;AAED;;GAEG;AACH,SAAsB,WAAW;;;YAC/B,IAAI,CAAC;gBACH,2EAA2E;gBAC3E,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;gBAElC,sBAAO,qBAAc,IAAI,CAAC,GAAG,EAAE,CAAE,EAAC;YACpC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;gBAClD,sBAAO,IAAI,EAAC;YACd,CAAC;;;;CACF;AAED;;GAEG;AACH,SAAsB,gBAAgB,CAAC,KAAa;;;YAClD,IAAI,CAAC;gBACH,yEAAyE;gBACzE,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;gBAEjD,sBAAO;wBACL,OAAO,EAAE,IAAI;qBACd,EAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;gBACzD,sBAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;qBAChE,EAAC;YACJ,CAAC;;;;CACF;AAED;;GAEG;AACH,SAAsB,oBAAoB,CAAC,KAAa;;;YACtD,IAAI,CAAC;gBACH,6EAA6E;gBAC7E,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;gBAErD,sBAAO;wBACL,OAAO,EAAE,IAAI;qBACd,EAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;gBAC7D,sBAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;qBAChE,EAAC;YACJ,CAAC;;;;CACF"}