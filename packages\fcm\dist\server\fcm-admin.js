"use strict";
/**
 * @file packages/fcm/src/server/fcm-admin.ts
 * @description Firebase Cloud Messaging admin utilities
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendToTopic = sendToTopic;
exports.sendToDevice = sendToDevice;
exports.sendToDevices = sendToDevices;
exports.subscribeToTopic = subscribeToTopic;
exports.unsubscribeFromTopic = unsubscribeFromTopic;
exports.validateToken = validateToken;
require("server-only");
// ========================================
// FCM ADMIN FUNCTIONS
// ========================================
/**
 * Send a message to a topic
 */
function sendToTopic(topic, message) {
    return __awaiter(this, void 0, void 0, function () {
        var error_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 2, , 3]);
                    // Mock implementation - in production, this would use Firebase Admin SDK
                    console.log('FCM: Sending message to topic:', topic, message);
                    // Simulate async operation
                    return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 100); })];
                case 1:
                    // Simulate async operation
                    _a.sent();
                    return [2 /*return*/, {
                            success: true,
                            messageId: "mock_message_".concat(Date.now()),
                        }];
                case 2:
                    error_1 = _a.sent();
                    console.error('FCM: Error sending message to topic:', error_1);
                    return [2 /*return*/, {
                            success: false,
                            error: error_1 instanceof Error ? error_1.message : 'Unknown error',
                        }];
                case 3: return [2 /*return*/];
            }
        });
    });
}
/**
 * Send a message to a specific device token
 */
function sendToDevice(token, message) {
    return __awaiter(this, void 0, void 0, function () {
        var error_2;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 2, , 3]);
                    // Mock implementation - in production, this would use Firebase Admin SDK
                    console.log('FCM: Sending message to device:', token, message);
                    // Simulate async operation
                    return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 100); })];
                case 1:
                    // Simulate async operation
                    _a.sent();
                    return [2 /*return*/, {
                            success: true,
                            messageId: "mock_message_".concat(Date.now()),
                        }];
                case 2:
                    error_2 = _a.sent();
                    console.error('FCM: Error sending message to device:', error_2);
                    return [2 /*return*/, {
                            success: false,
                            error: error_2 instanceof Error ? error_2.message : 'Unknown error',
                        }];
                case 3: return [2 /*return*/];
            }
        });
    });
}
/**
 * Send a message to multiple device tokens
 */
function sendToDevices(tokens, message) {
    return __awaiter(this, void 0, void 0, function () {
        var error_3;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 2, , 3]);
                    // Mock implementation - in production, this would use Firebase Admin SDK
                    console.log('FCM: Sending message to devices:', tokens.length, message);
                    // Simulate async operation
                    return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 100); })];
                case 1:
                    // Simulate async operation
                    _a.sent();
                    return [2 /*return*/, tokens.map(function (_, index) { return ({
                            success: true,
                            messageId: "mock_message_".concat(Date.now(), "_").concat(index),
                        }); })];
                case 2:
                    error_3 = _a.sent();
                    console.error('FCM: Error sending message to devices:', error_3);
                    return [2 /*return*/, tokens.map(function () { return ({
                            success: false,
                            error: error_3 instanceof Error ? error_3.message : 'Unknown error',
                        }); })];
                case 3: return [2 /*return*/];
            }
        });
    });
}
/**
 * Subscribe a device token to a topic
 */
function subscribeToTopic(token, topic) {
    return __awaiter(this, void 0, void 0, function () {
        var error_4;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 2, , 3]);
                    // Mock implementation - in production, this would use Firebase Admin SDK
                    console.log('FCM: Subscribing device to topic:', token, topic);
                    // Simulate async operation
                    return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 100); })];
                case 1:
                    // Simulate async operation
                    _a.sent();
                    return [2 /*return*/, {
                            success: true,
                        }];
                case 2:
                    error_4 = _a.sent();
                    console.error('FCM: Error subscribing to topic:', error_4);
                    return [2 /*return*/, {
                            success: false,
                            error: error_4 instanceof Error ? error_4.message : 'Unknown error',
                        }];
                case 3: return [2 /*return*/];
            }
        });
    });
}
/**
 * Unsubscribe a device token from a topic
 */
function unsubscribeFromTopic(token, topic) {
    return __awaiter(this, void 0, void 0, function () {
        var error_5;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 2, , 3]);
                    // Mock implementation - in production, this would use Firebase Admin SDK
                    console.log('FCM: Unsubscribing device from topic:', token, topic);
                    // Simulate async operation
                    return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 100); })];
                case 1:
                    // Simulate async operation
                    _a.sent();
                    return [2 /*return*/, {
                            success: true,
                        }];
                case 2:
                    error_5 = _a.sent();
                    console.error('FCM: Error unsubscribing from topic:', error_5);
                    return [2 /*return*/, {
                            success: false,
                            error: error_5 instanceof Error ? error_5.message : 'Unknown error',
                        }];
                case 3: return [2 /*return*/];
            }
        });
    });
}
/**
 * Validate a device token
 */
function validateToken(token) {
    return __awaiter(this, void 0, void 0, function () {
        return __generator(this, function (_a) {
            try {
                // Mock implementation - in production, this would validate with Firebase
                console.log('FCM: Validating token:', token);
                // Basic token format validation
                if (!token || token.length < 10) {
                    return [2 /*return*/, false];
                }
                return [2 /*return*/, true];
            }
            catch (error) {
                console.error('FCM: Error validating token:', error);
                return [2 /*return*/, false];
            }
            return [2 /*return*/];
        });
    });
}
//# sourceMappingURL=fcm-admin.js.map