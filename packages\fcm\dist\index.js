"use strict";
/**
 * @encreasl/fcm - Shared Firebase Cloud Messaging Package
 *
 * Provides FCM utilities, configurations, and React hooks
 * for the Encreasl monorepo. Supports both client and server-side usage.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateFCMEnvironment = exports.getFCMConfig = exports.createFCMConfig = exports.formatNotificationData = exports.createFCMMessage = exports.validateFCMConfig = exports.logFCMOperation = exports.generateMessageId = exports.createTokenBatch = exports.sanitizeDataPayload = exports.formatNotificationMessage = exports.validateToken = exports.serverUnsubscribeFromTopic = exports.serverSubscribeToTopic = exports.sendToDevices = exports.sendToDevice = exports.sendToTopic = exports.useNotificationPreferences = exports.useNotificationPermission = exports.useFCM = exports.logFCMEvent = exports.clientFormatNotificationData = exports.showNotification = exports.requestNotificationPermission = exports.isFCMSupported = exports.clientUnsubscribeFromTopic = exports.clientSubscribeToTopic = exports.getFCMToken = exports.initializeFCM = void 0;
// ========================================
// CORE EXPORTS
// ========================================
// Types
__exportStar(require("./types"), exports);
// Client-side utilities
var client_1 = require("./client");
Object.defineProperty(exports, "initializeFCM", { enumerable: true, get: function () { return client_1.initializeFCM; } });
Object.defineProperty(exports, "getFCMToken", { enumerable: true, get: function () { return client_1.getFCMToken; } });
Object.defineProperty(exports, "clientSubscribeToTopic", { enumerable: true, get: function () { return client_1.subscribeToTopic; } });
Object.defineProperty(exports, "clientUnsubscribeFromTopic", { enumerable: true, get: function () { return client_1.unsubscribeFromTopic; } });
Object.defineProperty(exports, "isFCMSupported", { enumerable: true, get: function () { return client_1.isFCMSupported; } });
Object.defineProperty(exports, "requestNotificationPermission", { enumerable: true, get: function () { return client_1.requestNotificationPermission; } });
Object.defineProperty(exports, "showNotification", { enumerable: true, get: function () { return client_1.showNotification; } });
Object.defineProperty(exports, "clientFormatNotificationData", { enumerable: true, get: function () { return client_1.formatNotificationData; } });
Object.defineProperty(exports, "logFCMEvent", { enumerable: true, get: function () { return client_1.logFCMEvent; } });
Object.defineProperty(exports, "useFCM", { enumerable: true, get: function () { return client_1.useFCM; } });
Object.defineProperty(exports, "useNotificationPermission", { enumerable: true, get: function () { return client_1.useNotificationPermission; } });
Object.defineProperty(exports, "useNotificationPreferences", { enumerable: true, get: function () { return client_1.useNotificationPreferences; } });
// Server-side utilities (for admin apps)
var server_1 = require("./server");
Object.defineProperty(exports, "sendToTopic", { enumerable: true, get: function () { return server_1.sendToTopic; } });
Object.defineProperty(exports, "sendToDevice", { enumerable: true, get: function () { return server_1.sendToDevice; } });
Object.defineProperty(exports, "sendToDevices", { enumerable: true, get: function () { return server_1.sendToDevices; } });
Object.defineProperty(exports, "serverSubscribeToTopic", { enumerable: true, get: function () { return server_1.subscribeToTopic; } });
Object.defineProperty(exports, "serverUnsubscribeFromTopic", { enumerable: true, get: function () { return server_1.unsubscribeFromTopic; } });
Object.defineProperty(exports, "validateToken", { enumerable: true, get: function () { return server_1.validateToken; } });
Object.defineProperty(exports, "formatNotificationMessage", { enumerable: true, get: function () { return server_1.formatNotificationMessage; } });
Object.defineProperty(exports, "sanitizeDataPayload", { enumerable: true, get: function () { return server_1.sanitizeDataPayload; } });
Object.defineProperty(exports, "createTokenBatch", { enumerable: true, get: function () { return server_1.createTokenBatch; } });
Object.defineProperty(exports, "generateMessageId", { enumerable: true, get: function () { return server_1.generateMessageId; } });
Object.defineProperty(exports, "logFCMOperation", { enumerable: true, get: function () { return server_1.logFCMOperation; } });
// Shared utilities
__exportStar(require("./utils"), exports);
// Configuration
__exportStar(require("./config"), exports);
// Common hooks are already exported above in client section
// Common utilities
var utils_1 = require("./utils");
Object.defineProperty(exports, "validateFCMConfig", { enumerable: true, get: function () { return utils_1.validateFCMConfig; } });
Object.defineProperty(exports, "createFCMMessage", { enumerable: true, get: function () { return utils_1.createFCMMessage; } });
Object.defineProperty(exports, "formatNotificationData", { enumerable: true, get: function () { return utils_1.formatNotificationData; } });
// Configuration helpers
var config_1 = require("./config");
Object.defineProperty(exports, "createFCMConfig", { enumerable: true, get: function () { return config_1.createFCMConfig; } });
Object.defineProperty(exports, "getFCMConfig", { enumerable: true, get: function () { return config_1.getFCMConfig; } });
Object.defineProperty(exports, "validateFCMEnvironment", { enumerable: true, get: function () { return config_1.validateFCMEnvironment; } });
//# sourceMappingURL=index.js.map