"use strict";
/**
 * @file packages/fcm/src/server/utils.ts
 * @description FCM server utilities
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateTopicName = void 0;
exports.formatNotificationMessage = formatNotificationMessage;
exports.sanitizeDataPayload = sanitizeDataPayload;
exports.createTokenBatch = createTokenBatch;
exports.generateMessageId = generateMessageId;
exports.logFCMOperation = logFCMOperation;
require("server-only");
// ========================================
// UTILITY FUNCTIONS
// ========================================
/**
 * Format a notification message for FCM
 */
function formatNotificationMessage(title, body, data) {
    return {
        title: title.substring(0, 100), // FCM title limit
        body: body.substring(0, 500), // FCM body limit
        data: data || {},
    };
}
/**
 * Validate topic name for FCM (re-exported from main utils)
 */
var utils_1 = require("../utils");
Object.defineProperty(exports, "validateTopicName", { enumerable: true, get: function () { return utils_1.validateTopicName; } });
/**
 * Sanitize data payload for FCM
 */
function sanitizeDataPayload(data) {
    var sanitized = {};
    for (var _i = 0, _a = Object.entries(data); _i < _a.length; _i++) {
        var _b = _a[_i], key = _b[0], value = _b[1];
        // FCM data payload must be string key-value pairs
        if (typeof key === 'string' && key.length > 0) {
            sanitized[key] = String(value);
        }
    }
    return sanitized;
}
/**
 * Create a batch of device tokens for multicast
 */
function createTokenBatch(tokens, batchSize) {
    if (batchSize === void 0) { batchSize = 500; }
    var batches = [];
    for (var i = 0; i < tokens.length; i += batchSize) {
        batches.push(tokens.slice(i, i + batchSize));
    }
    return batches;
}
/**
 * Generate a unique message ID
 */
function generateMessageId() {
    return "fcm_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9));
}
/**
 * Log FCM operation
 */
function logFCMOperation(operation, target, success, error) {
    var logData = {
        timestamp: new Date().toISOString(),
        operation: operation,
        target: target,
        success: success,
        error: error,
    };
    if (success) {
        console.log('FCM Operation Success:', logData);
    }
    else {
        console.error('FCM Operation Failed:', logData);
    }
}
//# sourceMappingURL=utils.js.map